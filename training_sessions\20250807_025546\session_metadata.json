{"session_id": "20250807_025546", "session_name": "GUI测试会话_20250807_025546", "description": "用于测试GUI集成功能的会话", "created_time": "2025-08-07T02:55:46.428781", "last_modified": "2025-08-07T02:55:46.778457", "trained_models": [{"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_025546.joblib", "filepath": "C:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_025546\\models\\RandomForest_single_025546.joblib", "save_time": "2025-08-07T02:55:46.487727"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_025546.joblib", "filepath": "C:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_025546\\models\\Logistic_single_025546.joblib", "save_time": "2025-08-07T02:55:46.680913"}], "ensemble_results": [], "data_files": [], "plots": [{"plot_name": "RandomForest_performance", "plot_type": "single_model", "model_name": "RandomForest", "filename": "RandomForest_RandomForest_performance_025546.png", "filepath": "C:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_025546\\plots\\single_model\\RandomForest_RandomForest_performance_025546.png", "save_time": "2025-08-07T02:55:46.674915"}, {"plot_name": "Logistic_performance", "plot_type": "single_model", "model_name": "Logistic", "filename": "Logistic_Logistic_performance_025546.png", "filepath": "C:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_025546\\plots\\single_model\\Logistic_Logistic_performance_025546.png", "save_time": "2025-08-07T02:55:46.778457"}], "logs": [], "status": "created"}