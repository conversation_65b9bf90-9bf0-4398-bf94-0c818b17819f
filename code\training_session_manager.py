#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练会话管理系统
负责管理训练过程中产生的所有文件，包括模型、图片、日志、配置等
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from joblib import dump, load
import logging

# 尝试导入配置
try:
    from config import SESSIONS_PATH, PROJECT_ROOT
    from logger import get_default_logger
except ImportError:
    # 如果导入失败，使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    SESSIONS_PATH = PROJECT_ROOT / 'training_sessions'
    SESSIONS_PATH.mkdir(parents=True, exist_ok=True)
    
    def get_default_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
        return logger

logger = get_default_logger("training_session_manager")


class TrainingSession:
    """训练会话类，管理单次训练的所有文件和元数据"""
    
    def __init__(self, session_id: str = None, session_name: str = None, description: str = ""):
        """
        初始化训练会话
        
        Args:
            session_id: 会话ID，如果为None则自动生成
            session_name: 会话名称，如果为None则使用时间戳
            description: 会话描述
        """
        if session_id is None:
            session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if session_name is None:
            session_name = f"训练会话_{session_id}"
        
        self.session_id = session_id
        self.session_name = session_name
        self.description = description
        self.created_time = datetime.now()
        self.last_modified = self.created_time
        
        # 创建会话目录结构
        self.session_path = SESSIONS_PATH / session_id
        self._create_directory_structure()
        
        # 初始化元数据
        self.metadata = {
            'session_id': session_id,
            'session_name': session_name,
            'description': description,
            'created_time': self.created_time.isoformat(),
            'last_modified': self.last_modified.isoformat(),
            'trained_models': [],
            'ensemble_results': [],
            'data_files': [],
            'plots': [],
            'logs': [],
            'status': 'created'
        }
        
        # 保存初始元数据
        self._save_metadata()
        
        logger.info(f"创建训练会话: {session_name} (ID: {session_id})")
    
    def _create_directory_structure(self):
        """创建标准化的会话目录结构"""
        directories = [
            'models',           # 保存训练好的模型文件
            'plots',           # 保存所有图片文件
            'plots/single_model',  # 单模型图片
            'plots/comparison',    # 模型比较图片
            'plots/ensemble',      # 集成学习图片
            'plots/shap',         # SHAP解释性图片
            'cache',           # 缓存文件
            'logs',            # 日志文件
            'config',          # 配置文件
            'reports',         # 报告文件
            'data_info'        # 数据信息
        ]
        
        for directory in directories:
            dir_path = self.session_path / directory
            dir_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"创建会话目录结构: {self.session_path}")
    
    def _save_metadata(self):
        """保存会话元数据"""
        metadata_file = self.session_path / 'session_metadata.json'
        self.last_modified = datetime.now()
        self.metadata['last_modified'] = self.last_modified.isoformat()
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)
    
    def get_path(self, path_type: str, filename: str = None) -> Path:
        """
        获取指定类型的文件路径
        
        Args:
            path_type: 路径类型 ('models', 'plots', 'cache', 'logs', 'config', 'reports', 'data_info')
            filename: 文件名，如果为None则返回目录路径
            
        Returns:
            Path: 文件或目录路径
        """
        base_path = self.session_path / path_type
        
        if filename is None:
            return base_path
        else:
            return base_path / filename
    
    def save_model(self, model: Any, model_name: str, model_type: str = 'single', 
                   additional_data: Dict = None) -> str:
        """
        保存模型到会话目录
        
        Args:
            model: 要保存的模型对象
            model_name: 模型名称
            model_type: 模型类型 ('single', 'ensemble', 'multi_data')
            additional_data: 额外的数据（如预测结果、特征名称等）
            
        Returns:
            str: 保存的文件路径
        """
        timestamp = datetime.now().strftime("%H%M%S")
        filename = f"{model_name}_{model_type}_{timestamp}.joblib"
        filepath = self.get_path('models', filename)
        
        # 准备保存的数据
        save_data = {
            'model': model,
            'model_name': model_name,
            'model_type': model_type,
            'save_time': datetime.now().isoformat(),
            'session_id': self.session_id
        }
        
        if additional_data:
            save_data.update(additional_data)
        
        # 保存模型
        dump(save_data, filepath)
        
        # 更新元数据
        model_info = {
            'model_name': model_name,
            'model_type': model_type,
            'filename': filename,
            'filepath': str(filepath),
            'save_time': save_data['save_time']
        }
        self.metadata['trained_models'].append(model_info)
        self._save_metadata()
        
        logger.info(f"保存模型 {model_name} 到: {filepath}")
        return str(filepath)
    
    def save_plot(self, fig, plot_name: str, plot_type: str, model_name: str = None) -> str:
        """
        保存图片到会话目录
        
        Args:
            fig: matplotlib图形对象
            plot_name: 图片名称
            plot_type: 图片类型 ('single_model', 'comparison', 'ensemble', 'shap')
            model_name: 相关的模型名称（可选）
            
        Returns:
            str: 保存的文件路径
        """
        timestamp = datetime.now().strftime("%H%M%S")
        
        if model_name:
            filename = f"{model_name}_{plot_name}_{timestamp}.png"
        else:
            filename = f"{plot_name}_{timestamp}.png"
        
        # 根据图片类型选择子目录
        if plot_type in ['single_model', 'comparison', 'ensemble', 'shap']:
            filepath = self.get_path('plots') / plot_type / filename
        else:
            filepath = self.get_path('plots', filename)
        
        # 确保目录存在
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存图片
        fig.savefig(filepath, dpi=300, bbox_inches='tight')
        
        # 更新元数据
        plot_info = {
            'plot_name': plot_name,
            'plot_type': plot_type,
            'model_name': model_name,
            'filename': filename,
            'filepath': str(filepath),
            'save_time': datetime.now().isoformat()
        }
        self.metadata['plots'].append(plot_info)
        self._save_metadata()
        
        logger.info(f"保存图片 {plot_name} 到: {filepath}")
        return str(filepath)
    
    def save_cache(self, data: Any, cache_name: str) -> str:
        """
        保存缓存数据到会话目录
        
        Args:
            data: 要缓存的数据
            cache_name: 缓存名称
            
        Returns:
            str: 保存的文件路径
        """
        filename = f"{cache_name}.joblib"
        filepath = self.get_path('cache', filename)
        
        dump(data, filepath)
        
        logger.info(f"保存缓存 {cache_name} 到: {filepath}")
        return str(filepath)
    
    def save_config(self, config_data: Dict, config_name: str) -> str:
        """
        保存配置文件到会话目录
        
        Args:
            config_data: 配置数据
            config_name: 配置名称
            
        Returns:
            str: 保存的文件路径
        """
        filename = f"{config_name}.json"
        filepath = self.get_path('config', filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"保存配置 {config_name} 到: {filepath}")
        return str(filepath)
    
    def update_status(self, status: str):
        """更新会话状态"""
        self.metadata['status'] = status
        self._save_metadata()
        logger.info(f"更新会话状态为: {status}")
    
    def add_data_file_info(self, data_path: str, data_info: Dict = None):
        """添加数据文件信息"""
        data_file_info = {
            'data_path': data_path,
            'added_time': datetime.now().isoformat()
        }
        
        if data_info:
            data_file_info.update(data_info)
        
        self.metadata['data_files'].append(data_file_info)
        self._save_metadata()
        logger.info(f"添加数据文件信息: {data_path}")


class TrainingSessionManager:
    """训练会话管理器，负责管理所有训练会话"""
    
    def __init__(self):
        """初始化会话管理器"""
        self.sessions_path = SESSIONS_PATH
        self.current_session: Optional[TrainingSession] = None
        
        # 确保会话根目录存在
        self.sessions_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("初始化训练会话管理器")
    
    def create_session(self, session_name: str = None, description: str = "") -> TrainingSession:
        """
        创建新的训练会话
        
        Args:
            session_name: 会话名称
            description: 会话描述
            
        Returns:
            TrainingSession: 新创建的会话对象
        """
        session = TrainingSession(session_name=session_name, description=description)
        self.current_session = session
        
        logger.info(f"创建新会话: {session.session_name}")
        return session
    
    def load_session(self, session_id: str) -> Optional[TrainingSession]:
        """
        加载指定的训练会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            TrainingSession: 加载的会话对象，如果失败则返回None
        """
        session_path = self.sessions_path / session_id
        metadata_file = session_path / 'session_metadata.json'
        
        if not metadata_file.exists():
            logger.error(f"会话元数据文件不存在: {metadata_file}")
            return None
        
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 重建会话对象
            session = TrainingSession.__new__(TrainingSession)
            session.session_id = metadata['session_id']
            session.session_name = metadata['session_name']
            session.description = metadata['description']
            session.created_time = datetime.fromisoformat(metadata['created_time'])
            session.last_modified = datetime.fromisoformat(metadata['last_modified'])
            session.session_path = session_path
            session.metadata = metadata
            
            self.current_session = session
            logger.info(f"成功加载会话: {session.session_name}")
            return session
            
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
            return None
    
    def list_sessions(self) -> List[Dict]:
        """
        列出所有可用的训练会话
        
        Returns:
            List[Dict]: 会话信息列表
        """
        sessions = []
        
        for session_dir in self.sessions_path.iterdir():
            if session_dir.is_dir():
                metadata_file = session_dir / 'session_metadata.json'
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        
                        session_info = {
                            'session_id': metadata['session_id'],
                            'session_name': metadata['session_name'],
                            'description': metadata['description'],
                            'created_time': metadata['created_time'],
                            'last_modified': metadata['last_modified'],
                            'status': metadata.get('status', 'unknown'),
                            'model_count': len(metadata.get('trained_models', [])),
                            'plot_count': len(metadata.get('plots', []))
                        }
                        sessions.append(session_info)
                        
                    except Exception as e:
                        logger.warning(f"读取会话元数据失败 {session_dir.name}: {e}")
        
        # 按创建时间排序
        sessions.sort(key=lambda x: x['created_time'], reverse=True)
        return sessions
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除指定的训练会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 删除是否成功
        """
        session_path = self.sessions_path / session_id
        
        if not session_path.exists():
            logger.error(f"会话目录不存在: {session_path}")
            return False
        
        try:
            shutil.rmtree(session_path)
            logger.info(f"成功删除会话: {session_id}")
            
            # 如果删除的是当前会话，清空当前会话
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session = None
            
            return True
            
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False
    
    def get_current_session(self) -> Optional[TrainingSession]:
        """获取当前活动的会话"""
        return self.current_session
    
    def set_current_session(self, session_id: str) -> bool:
        """
        设置当前活动的会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 设置是否成功
        """
        session = self.load_session(session_id)
        if session:
            self.current_session = session
            return True
        return False


# 全局会话管理器实例
session_manager = TrainingSessionManager()


def get_session_manager() -> TrainingSessionManager:
    """获取全局会话管理器实例"""
    return session_manager


def get_current_session() -> Optional[TrainingSession]:
    """获取当前活动的会话"""
    return session_manager.get_current_session()
