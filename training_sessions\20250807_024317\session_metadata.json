{"session_id": "20250807_024317", "session_name": "训练_nodule2_20250807_024317", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-07T02:43:17.869540", "last_modified": "2025-08-07T02:43:19.661458", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_024317.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\DecisionTree_single_024317.joblib", "save_time": "2025-08-07T02:43:17.914047"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_024318.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\RandomForest_single_024318.joblib", "save_time": "2025-08-07T02:43:18.017769"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_024318.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\XGBoost_single_024318.joblib", "save_time": "2025-08-07T02:43:18.124289"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_024318.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\LightGBM_single_024318.joblib", "save_time": "2025-08-07T02:43:18.202431"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\CatBoost_single_024319.joblib", "save_time": "2025-08-07T02:43:19.253758"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\Logistic_single_024319.joblib", "save_time": "2025-08-07T02:43:19.286043"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\SVM_single_024319.joblib", "save_time": "2025-08-07T02:43:19.316838"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\KNN_single_024319.joblib", "save_time": "2025-08-07T02:43:19.347111"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\NaiveBayes_single_024319.joblib", "save_time": "2025-08-07T02:43:19.389208"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_024319.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_024317\\models\\NeuralNet_single_024319.joblib", "save_time": "2025-08-07T02:43:19.649613"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}