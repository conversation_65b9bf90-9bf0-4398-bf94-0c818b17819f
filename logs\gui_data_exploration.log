2025-07-18 19:54:07 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-18 19:55:46 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-18 19:57:07 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-18 19:57:43 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-18 19:58:45 - gui_data_exploration - ERROR - 分析过程中出现错误: 'yerr' must not contain negative values
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 118, in create_binned_probability_analysis
    self._create_probability_bar_chart(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 212, in _create_probability_bar_chart
    ax1.errorbar(x_pos, grouped_data['probability'],
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\__init__.py", line 1476, in inner
    return func(
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\axes\_axes.py", line 3743, in errorbar
    raise ValueError(
ValueError: 'yerr' must not contain negative values
2025-07-18 19:59:16 - gui_data_exploration - ERROR - 分析过程中出现错误: 'yerr' must not contain negative values
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 118, in create_binned_probability_analysis
    self._create_probability_bar_chart(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 212, in _create_probability_bar_chart
    ax1.errorbar(x_pos, grouped_data['probability'],
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\__init__.py", line 1476, in inner
    return func(
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\axes\_axes.py", line 3743, in errorbar
    raise ValueError(
ValueError: 'yerr' must not contain negative values
2025-07-18 20:00:51 - gui_data_exploration - ERROR - 分析过程中出现错误: 'yerr' must not contain negative values
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 118, in create_binned_probability_analysis
    self._create_probability_bar_chart(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 212, in _create_probability_bar_chart
    ax1.errorbar(x_pos, grouped_data['probability'],
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\__init__.py", line 1476, in inner
    return func(
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\axes\_axes.py", line 3743, in errorbar
    raise ValueError(
ValueError: 'yerr' must not contain negative values
2025-07-18 20:07:18 - gui_data_exploration - ERROR - 分析过程中出现错误: 'yerr' must not contain negative values
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 118, in create_binned_probability_analysis
    self._create_probability_bar_chart(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 212, in _create_probability_bar_chart
    ax1.errorbar(x_pos, grouped_data['probability'],
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\__init__.py", line 1476, in inner
    return func(
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\axes\_axes.py", line 3743, in errorbar
    raise ValueError(
ValueError: 'yerr' must not contain negative values
2025-07-18 20:07:21 - gui_data_exploration - ERROR - 分析过程中出现错误: 'yerr' must not contain negative values
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 118, in create_binned_probability_analysis
    self._create_probability_bar_chart(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 212, in _create_probability_bar_chart
    ax1.errorbar(x_pos, grouped_data['probability'],
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\__init__.py", line 1476, in inner
    return func(
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\axes\_axes.py", line 3743, in errorbar
    raise ValueError(
ValueError: 'yerr' must not contain negative values
2025-07-18 20:14:55 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-18 20:18:04 - gui_data_exploration - ERROR - 分析过程中出现错误: list index out of range
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 124, in create_binned_probability_analysis
    self._create_comprehensive_comparison(results, target_var)
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 317, in _create_comprehensive_comparison
    color=colors[idx], alpha=0.8,
IndexError: list index out of range
2025-07-18 20:24:04 - gui_data_exploration - ERROR - 分析过程中出现错误: list index out of range
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 124, in create_binned_probability_analysis
    self._create_comprehensive_comparison(results, target_var)
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 317, in _create_comprehensive_comparison
    color=colors[idx], alpha=0.8,
IndexError: list index out of range
2025-07-18 20:24:40 - gui_data_exploration - ERROR - 分析过程中出现错误: list index out of range
Traceback (most recent call last):
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\gui_data_exploration.py", line 385, in _run_analysis
    prob_results = self.explorer.create_binned_probability_analysis(
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 124, in create_binned_probability_analysis
    self._create_comprehensive_comparison(results, target_var)
  File "c:\Users\<USER>\PycharmProjects\multi_model_01_updated\code\data_exploration.py", line 317, in _create_comprehensive_comparison
    color=colors[idx], alpha=0.8,
IndexError: list index out of range
2025-07-18 20:45:41 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-19 01:34:22 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-19 01:49:35 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-19 02:36:30 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 00:22:26 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:07:04 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:30:46 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:36:19 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:46:03 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:52:38 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 01:58:44 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:02:19 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:12:03 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:15:56 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:40:54 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 02:46:29 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
