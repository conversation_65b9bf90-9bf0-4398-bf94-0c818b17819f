#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练会话加载器
负责完整恢复之前训练会话的所有结果和状态
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from joblib import load
import logging
import matplotlib.pyplot as plt

# 尝试导入相关模块
try:
    from training_session_manager import TrainingSessionManager, TrainingSession
    from logger import get_default_logger
    from config import SESSIONS_PATH
except ImportError:
    # 如果导入失败，使用默认配置
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    SESSIONS_PATH = PROJECT_ROOT / 'training_sessions'
    
    def get_default_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logger.addHandler(handler)
        return logger

logger = get_default_logger("session_loader")


class SessionLoader:
    """会话加载器，负责恢复训练会话的完整状态"""
    
    def __init__(self, session_manager: TrainingSessionManager = None):
        """
        初始化会话加载器
        
        Args:
            session_manager: 会话管理器实例
        """
        if session_manager is None:
            from training_session_manager import get_session_manager
            session_manager = get_session_manager()
        
        self.session_manager = session_manager
        logger.info("初始化会话加载器")
    
    def load_session_models(self, session: TrainingSession) -> Dict[str, Any]:
        """
        加载会话中的所有模型
        
        Args:
            session: 训练会话对象
            
        Returns:
            Dict[str, Any]: 加载的模型字典
        """
        models = {}
        
        for model_info in session.metadata.get('trained_models', []):
            model_name = model_info['model_name']
            filepath = Path(model_info['filepath'])
            
            if filepath.exists():
                try:
                    model_data = load(filepath)
                    models[model_name] = model_data
                    logger.info(f"成功加载模型: {model_name}")
                except Exception as e:
                    logger.error(f"加载模型 {model_name} 失败: {e}")
            else:
                logger.warning(f"模型文件不存在: {filepath}")
        
        return models
    
    def load_session_cache(self, session: TrainingSession) -> Dict[str, Any]:
        """
        加载会话中的所有缓存数据
        
        Args:
            session: 训练会话对象
            
        Returns:
            Dict[str, Any]: 加载的缓存数据字典
        """
        cache_data = {}
        cache_dir = session.get_path('cache')
        
        if cache_dir.exists():
            for cache_file in cache_dir.glob('*.joblib'):
                cache_name = cache_file.stem
                try:
                    data = load(cache_file)
                    cache_data[cache_name] = data
                    logger.info(f"成功加载缓存: {cache_name}")
                except Exception as e:
                    logger.error(f"加载缓存 {cache_name} 失败: {e}")
        
        return cache_data
    
    def load_session_configs(self, session: TrainingSession) -> Dict[str, Any]:
        """
        加载会话中的所有配置文件
        
        Args:
            session: 训练会话对象
            
        Returns:
            Dict[str, Any]: 加载的配置数据字典
        """
        configs = {}
        config_dir = session.get_path('config')
        
        if config_dir.exists():
            for config_file in config_dir.glob('*.json'):
                config_name = config_file.stem
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    configs[config_name] = config_data
                    logger.info(f"成功加载配置: {config_name}")
                except Exception as e:
                    logger.error(f"加载配置 {config_name} 失败: {e}")
        
        return configs
    
    def get_session_plots(self, session: TrainingSession) -> Dict[str, List[Dict]]:
        """
        获取会话中的所有图片信息
        
        Args:
            session: 训练会话对象
            
        Returns:
            Dict[str, List[Dict]]: 按类型分组的图片信息
        """
        plots_by_type = {
            'single_model': [],
            'comparison': [],
            'ensemble': [],
            'shap': [],
            'other': []
        }
        
        for plot_info in session.metadata.get('plots', []):
            plot_type = plot_info.get('plot_type', 'other')
            if plot_type not in plots_by_type:
                plot_type = 'other'
            
            # 检查文件是否存在
            filepath = Path(plot_info['filepath'])
            plot_info['exists'] = filepath.exists()
            
            plots_by_type[plot_type].append(plot_info)
        
        return plots_by_type
    
    def restore_session_to_cache(self, session: TrainingSession, target_cache_path: Path = None) -> bool:
        """
        将会话数据恢复到项目缓存目录，以便其他模块使用
        
        Args:
            session: 训练会话对象
            target_cache_path: 目标缓存路径，如果为None则使用项目默认缓存路径
            
        Returns:
            bool: 恢复是否成功
        """
        if target_cache_path is None:
            try:
                from config import CACHE_PATH
                target_cache_path = CACHE_PATH
            except ImportError:
                PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                target_cache_path = PROJECT_ROOT / 'cache'
        
        target_cache_path.mkdir(parents=True, exist_ok=True)
        
        try:
            # 恢复模型缓存
            models = self.load_session_models(session)
            for model_name, model_data in models.items():
                # 保存模型结果到标准缓存格式
                cache_file = target_cache_path / f"{model_name}_results.joblib"
                
                # 提取标准格式的数据
                if isinstance(model_data, dict) and 'model' in model_data:
                    # 如果是完整的模型数据包
                    standard_data = {
                        'model': model_data['model'],
                        'y_true': model_data.get('y_true'),
                        'y_pred': model_data.get('y_pred'),
                        'y_pred_proba': model_data.get('y_pred_proba'),
                        'X_test': model_data.get('X_test')
                    }

                    # 如果有特征名称，也添加进去
                    if 'feature_names' in model_data:
                        standard_data['feature_names'] = model_data['feature_names']

                    # 确保数据完整性，如果缺少关键数据则尝试从模型重新生成
                    if standard_data['X_test'] is None or standard_data['y_true'] is None:
                        logger.warning(f"模型 {model_name} 的测试数据不完整，跳过恢复")
                        continue

                else:
                    # 如果只是模型对象，跳过（数据不完整）
                    logger.warning(f"模型 {model_name} 数据格式不完整，跳过恢复")
                    continue
                
                from joblib import dump
                dump(standard_data, cache_file)
                logger.info(f"恢复模型缓存: {model_name}")
                
                # 如果有特征名称，也恢复
                if 'feature_names' in model_data:
                    feature_names_file = target_cache_path / f"{model_name}_feature_names.joblib"
                    dump(model_data['feature_names'], feature_names_file)
            
            # 恢复其他缓存数据
            cache_data = self.load_session_cache(session)
            for cache_name, data in cache_data.items():
                cache_file = target_cache_path / f"{cache_name}.joblib"
                from joblib import dump
                dump(data, cache_file)
                logger.info(f"恢复缓存数据: {cache_name}")
            
            # 恢复最后使用的数据文件信息
            if session.metadata.get('data_files'):
                last_data_file = session.metadata['data_files'][-1]['data_path']
                data_file_cache = target_cache_path / 'last_used_data_file.txt'
                with open(data_file_cache, 'w', encoding='utf-8') as f:
                    f.write(last_data_file)
                logger.info(f"恢复数据文件路径: {last_data_file}")
            
            logger.info(f"成功恢复会话 {session.session_name} 到缓存目录")
            return True
            
        except Exception as e:
            logger.error(f"恢复会话到缓存失败: {e}")
            return False
    
    def get_session_summary(self, session: TrainingSession) -> Dict[str, Any]:
        """
        获取会话的完整摘要信息
        
        Args:
            session: 训练会话对象
            
        Returns:
            Dict[str, Any]: 会话摘要信息
        """
        summary = {
            'basic_info': {
                'session_id': session.session_id,
                'session_name': session.session_name,
                'description': session.description,
                'created_time': session.created_time.isoformat(),
                'last_modified': session.last_modified.isoformat(),
                'status': session.metadata.get('status', 'unknown')
            },
            'statistics': {
                'total_models': len(session.metadata.get('trained_models', [])),
                'total_plots': len(session.metadata.get('plots', [])),
                'total_data_files': len(session.metadata.get('data_files', [])),
                'ensemble_results': len(session.metadata.get('ensemble_results', []))
            },
            'models': [],
            'plots_by_type': {},
            'data_files': session.metadata.get('data_files', [])
        }
        
        # 模型详情
        for model_info in session.metadata.get('trained_models', []):
            model_summary = {
                'name': model_info['model_name'],
                'type': model_info['model_type'],
                'save_time': model_info['save_time'],
                'file_exists': Path(model_info['filepath']).exists()
            }
            summary['models'].append(model_summary)
        
        # 图片统计
        plots_by_type = self.get_session_plots(session)
        for plot_type, plots in plots_by_type.items():
            summary['plots_by_type'][plot_type] = {
                'count': len(plots),
                'plots': plots
            }
        
        return summary
    
    def compare_sessions(self, session_ids: List[str]) -> Dict[str, Any]:
        """
        比较多个训练会话
        
        Args:
            session_ids: 要比较的会话ID列表
            
        Returns:
            Dict[str, Any]: 比较结果
        """
        comparison = {
            'sessions': [],
            'comparison_metrics': {
                'model_counts': {},
                'plot_counts': {},
                'creation_times': {},
                'statuses': {}
            }
        }
        
        for session_id in session_ids:
            session = self.session_manager.load_session(session_id)
            if session:
                summary = self.get_session_summary(session)
                comparison['sessions'].append(summary)
                
                # 收集比较指标
                comparison['comparison_metrics']['model_counts'][session_id] = summary['statistics']['total_models']
                comparison['comparison_metrics']['plot_counts'][session_id] = summary['statistics']['total_plots']
                comparison['comparison_metrics']['creation_times'][session_id] = summary['basic_info']['created_time']
                comparison['comparison_metrics']['statuses'][session_id] = summary['basic_info']['status']
        
        return comparison
    
    def export_session_report(self, session: TrainingSession, output_path: Path = None) -> str:
        """
        导出会话的详细报告
        
        Args:
            session: 训练会话对象
            output_path: 输出路径，如果为None则保存到会话目录
            
        Returns:
            str: 报告文件路径
        """
        if output_path is None:
            output_path = session.get_path('reports', 'session_report.html')
        
        summary = self.get_session_summary(session)
        
        # 生成HTML报告
        html_content = self._generate_html_report(summary)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"导出会话报告到: {output_path}")
        return str(output_path)
    
    def _generate_html_report(self, summary: Dict[str, Any]) -> str:
        """生成HTML格式的会话报告"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>训练会话报告 - {summary['basic_info']['session_name']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .model-list {{ list-style-type: none; padding: 0; }}
                .model-item {{ background-color: #f9f9f9; margin: 5px 0; padding: 10px; border-radius: 3px; }}
                .stats {{ display: flex; justify-content: space-around; }}
                .stat-item {{ text-align: center; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>训练会话报告</h1>
                <h2>{summary['basic_info']['session_name']}</h2>
                <p><strong>会话ID:</strong> {summary['basic_info']['session_id']}</p>
                <p><strong>描述:</strong> {summary['basic_info']['description']}</p>
                <p><strong>创建时间:</strong> {summary['basic_info']['created_time']}</p>
                <p><strong>最后修改:</strong> {summary['basic_info']['last_modified']}</p>
                <p><strong>状态:</strong> {summary['basic_info']['status']}</p>
            </div>
            
            <div class="section">
                <h3>统计信息</h3>
                <div class="stats">
                    <div class="stat-item">
                        <h4>{summary['statistics']['total_models']}</h4>
                        <p>训练模型</p>
                    </div>
                    <div class="stat-item">
                        <h4>{summary['statistics']['total_plots']}</h4>
                        <p>生成图片</p>
                    </div>
                    <div class="stat-item">
                        <h4>{summary['statistics']['total_data_files']}</h4>
                        <p>数据文件</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h3>训练模型</h3>
                <table>
                    <tr>
                        <th>模型名称</th>
                        <th>模型类型</th>
                        <th>保存时间</th>
                        <th>文件状态</th>
                    </tr>
        """
        
        for model in summary['models']:
            status = "✓ 存在" if model['file_exists'] else "✗ 缺失"
            html += f"""
                    <tr>
                        <td>{model['name']}</td>
                        <td>{model['type']}</td>
                        <td>{model['save_time']}</td>
                        <td>{status}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
            
            <div class="section">
                <h3>图片统计</h3>
                <table>
                    <tr>
                        <th>图片类型</th>
                        <th>数量</th>
                    </tr>
        """
        
        for plot_type, plot_info in summary['plots_by_type'].items():
            if plot_info['count'] > 0:
                html += f"""
                    <tr>
                        <td>{plot_type}</td>
                        <td>{plot_info['count']}</td>
                    </tr>
                """
        
        html += """
                </table>
            </div>
        </body>
        </html>
        """
        
        return html


def get_session_loader() -> SessionLoader:
    """获取会话加载器实例"""
    return SessionLoader()
